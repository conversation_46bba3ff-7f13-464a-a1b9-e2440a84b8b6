"""
向量存储模块
负责文档向量化和FAISS向量数据库的管理
"""

import os
from typing import List, Optional
from langchain.embeddings import OpenAIEmbeddings
from langchain.vectorstores import FAISS
from langchain.schema import Document


class VectorStore:
    """向量存储管理器"""
    
    def __init__(self, embedding_model: str = "text-embedding-3-small"):
        """
        初始化向量存储管理器
        
        Args:
            embedding_model: 使用的embedding模型名称
        """
        self.embedding_model = embedding_model
        self.embeddings = OpenAIEmbeddings(model=embedding_model)
        self.vectorstore = None
        
    def create_vectorstore(self, documents: List[Document]) -> FAISS:
        """
        从文档创建向量存储
        
        Args:
            documents: 文档列表
            
        Returns:
            FAISS向量存储对象
        """
        if not documents:
            raise ValueError("文档列表不能为空")
        
        try:
            print(f"开始向量化 {len(documents)} 个文档块...")
            self.vectorstore = FAISS.from_documents(documents, self.embeddings)
            print("向量化完成")
            return self.vectorstore
        except Exception as e:
            raise Exception(f"创建向量存储失败: {str(e)}")
    
    def save_vectorstore(self, save_path: str) -> None:
        """
        保存向量存储到本地
        
        Args:
            save_path: 保存路径
        """
        if self.vectorstore is None:
            raise ValueError("向量存储未初始化，请先创建向量存储")
        
        try:
            # 确保保存目录存在
            os.makedirs(save_path, exist_ok=True)
            self.vectorstore.save_local(save_path)
            print(f"向量存储已保存到: {save_path}")
        except Exception as e:
            raise Exception(f"保存向量存储失败: {str(e)}")
    
    def load_vectorstore(self, load_path: str) -> FAISS:
        """
        从本地加载向量存储
        
        Args:
            load_path: 加载路径
            
        Returns:
            FAISS向量存储对象
        """
        if not os.path.exists(load_path):
            raise FileNotFoundError(f"向量存储路径不存在: {load_path}")
        
        try:
            self.vectorstore = FAISS.load_local(load_path, self.embeddings)
            print(f"向量存储已从 {load_path} 加载")
            return self.vectorstore
        except Exception as e:
            raise Exception(f"加载向量存储失败: {str(e)}")
    
    def add_documents(self, documents: List[Document]) -> None:
        """
        向现有向量存储添加新文档
        
        Args:
            documents: 要添加的文档列表
        """
        if self.vectorstore is None:
            raise ValueError("向量存储未初始化，请先创建或加载向量存储")
        
        if not documents:
            raise ValueError("文档列表不能为空")
        
        try:
            print(f"向向量存储添加 {len(documents)} 个新文档...")
            self.vectorstore.add_documents(documents)
            print("文档添加完成")
        except Exception as e:
            raise Exception(f"添加文档失败: {str(e)}")
    
    def similarity_search(self, query: str, k: int = 4) -> List[Document]:
        """
        相似度搜索
        
        Args:
            query: 查询文本
            k: 返回的文档数量
            
        Returns:
            相似文档列表
        """
        if self.vectorstore is None:
            raise ValueError("向量存储未初始化，请先创建或加载向量存储")
        
        try:
            results = self.vectorstore.similarity_search(query, k=k)
            return results
        except Exception as e:
            raise Exception(f"相似度搜索失败: {str(e)}")
    
    def similarity_search_with_score(self, query: str, k: int = 4) -> List[tuple]:
        """
        带分数的相似度搜索
        
        Args:
            query: 查询文本
            k: 返回的文档数量
            
        Returns:
            (文档, 相似度分数)的元组列表
        """
        if self.vectorstore is None:
            raise ValueError("向量存储未初始化，请先创建或加载向量存储")
        
        try:
            results = self.vectorstore.similarity_search_with_score(query, k=k)
            return results
        except Exception as e:
            raise Exception(f"带分数的相似度搜索失败: {str(e)}")
    
    def get_retriever(self, search_kwargs: Optional[dict] = None):
        """
        获取检索器对象
        
        Args:
            search_kwargs: 搜索参数
            
        Returns:
            检索器对象
        """
        if self.vectorstore is None:
            raise ValueError("向量存储未初始化，请先创建或加载向量存储")
        
        if search_kwargs is None:
            search_kwargs = {"k": 4}
        
        return self.vectorstore.as_retriever(search_kwargs=search_kwargs)
    
    def get_vectorstore_info(self) -> dict:
        """
        获取向量存储信息
        
        Returns:
            向量存储信息字典
        """
        if self.vectorstore is None:
            return {"status": "未初始化", "document_count": 0}
        
        try:
            # FAISS没有直接获取文档数量的方法，这里返回基本信息
            return {
                "status": "已初始化",
                "embedding_model": self.embedding_model,
                "vectorstore_type": "FAISS"
            }
        except Exception as e:
            return {"status": f"错误: {str(e)}"}


if __name__ == "__main__":
    # 测试代码
    vector_store = VectorStore()
    
    # 示例：创建和使用向量存储
    # documents = [...]  # 从document_processor获取的文档
    # vector_store.create_vectorstore(documents)
    # vector_store.save_vectorstore("./vector_index")
    # results = vector_store.similarity_search("查询文本")
    # print(f"搜索结果: {len(results)} 个文档")
