"""
PDF文档处理模块
负责PDF文档的加载、解析和智能切分
"""

import os
from typing import List, Optional
from langchain_community.document_loaders import PyMuPDFLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_core.documents import Document


class DocumentProcessor:
    """PDF文档处理器"""
    
    def __init__(self, chunk_size: int = 1500, chunk_overlap: int = 200):
        """
        初始化文档处理器
        
        Args:
            chunk_size: 文档切分的块大小
            chunk_overlap: 块之间的重叠大小
        """
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        
        # 初始化文本切分器
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            separators=["\n\n", "\n", ".", "。", "！", "？", " ", ""]
        )
    
    def load_pdf(self, pdf_path: str) -> List[Document]:
        """
        加载PDF文档
        
        Args:
            pdf_path: PDF文件路径
            
        Returns:
            文档列表
        """
        if not os.path.exists(pdf_path):
            raise FileNotFoundError(f"PDF文件不存在: {pdf_path}")
        
        if not pdf_path.lower().endswith('.pdf'):
            raise ValueError("文件必须是PDF格式")
        
        try:
            loader = PyMuPDFLoader(pdf_path)
            documents = loader.load()
            
            # 添加文件名到metadata
            for doc in documents:
                doc.metadata['source_file'] = os.path.basename(pdf_path)
            
            return documents
        except Exception as e:
            raise Exception(f"加载PDF文件失败: {str(e)}")
    
    def split_documents(self, documents: List[Document]) -> List[Document]:
        """
        切分文档为小块
        
        Args:
            documents: 原始文档列表
            
        Returns:
            切分后的文档块列表
        """
        try:
            split_docs = self.text_splitter.split_documents(documents)
            
            # 为每个切分块添加唯一标识
            for i, doc in enumerate(split_docs):
                doc.metadata['chunk_id'] = i
                doc.metadata['chunk_size'] = len(doc.page_content)
            
            return split_docs
        except Exception as e:
            raise Exception(f"文档切分失败: {str(e)}")
    
    def process_pdf(self, pdf_path: str) -> List[Document]:
        """
        完整处理PDF文档：加载 + 切分
        
        Args:
            pdf_path: PDF文件路径
            
        Returns:
            处理后的文档块列表
        """
        # 加载PDF
        documents = self.load_pdf(pdf_path)
        print(f"成功加载PDF文档，共 {len(documents)} 页")
        
        # 切分文档
        split_docs = self.split_documents(documents)
        print(f"文档切分完成，共生成 {len(split_docs)} 个文档块")
        
        return split_docs
    
    def get_document_info(self, documents: List[Document]) -> dict:
        """
        获取文档信息统计
        
        Args:
            documents: 文档列表
            
        Returns:
            文档信息字典
        """
        if not documents:
            return {"total_chunks": 0, "total_chars": 0, "avg_chunk_size": 0}
        
        total_chars = sum(len(doc.page_content) for doc in documents)
        avg_chunk_size = total_chars / len(documents)
        
        return {
            "total_chunks": len(documents),
            "total_chars": total_chars,
            "avg_chunk_size": round(avg_chunk_size, 2),
            "source_files": list(set(doc.metadata.get('source_file', 'unknown') for doc in documents))
        }


if __name__ == "__main__":
    # 测试代码
    processor = DocumentProcessor()
    
    # 示例：处理PDF文件
    # documents = processor.process_pdf("example.pdf")
    # info = processor.get_document_info(documents)
    # print(f"文档信息: {info}")
