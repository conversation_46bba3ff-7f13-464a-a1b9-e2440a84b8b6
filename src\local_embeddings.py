"""
本地Embedding模块
使用sentence-transformers提供本地向量化能力，无需API调用
"""

import os
from typing import List, Optional
import numpy as np
from langchain_core.embeddings import Embeddings

try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False
    print("警告: sentence-transformers未安装，请运行: pip install sentence-transformers")


class LocalEmbeddings(Embeddings):
    """本地Embedding类，兼容LangChain接口"""
    
    def __init__(self, model_name: str = "all-MiniLM-L6-v2"):
        """
        初始化本地embedding模型
        
        Args:
            model_name: 模型名称，推荐使用:
                - "all-MiniLM-L6-v2": 轻量级，速度快
                - "all-mpnet-base-v2": 质量更好，稍慢
                - "paraphrase-multilingual-MiniLM-L12-v2": 多语言支持
        """
        if not SENTENCE_TRANSFORMERS_AVAILABLE:
            raise ImportError("请安装sentence-transformers: pip install sentence-transformers")
        
        self.model_name = model_name
        print(f"正在加载本地embedding模型: {model_name}")
        
        try:
            self.model = SentenceTransformer(model_name)
            print(f"✓ 模型加载成功: {model_name}")
        except Exception as e:
            print(f"✗ 模型加载失败: {e}")
            print("尝试使用备用模型...")
            try:
                self.model = SentenceTransformer("all-MiniLM-L6-v2")
                self.model_name = "all-MiniLM-L6-v2"
                print("✓ 备用模型加载成功")
            except Exception as e2:
                raise Exception(f"无法加载任何embedding模型: {e2}")
    
    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """
        对文档列表进行向量化
        
        Args:
            texts: 文本列表
            
        Returns:
            向量列表
        """
        if not texts:
            return []
        
        try:
            # 使用sentence-transformers进行编码
            embeddings = self.model.encode(texts, convert_to_tensor=False)
            
            # 确保返回的是List[List[float]]格式
            if isinstance(embeddings, np.ndarray):
                embeddings = embeddings.tolist()
            
            return embeddings
            
        except Exception as e:
            raise Exception(f"文档向量化失败: {str(e)}")
    
    def embed_query(self, text: str) -> List[float]:
        """
        对查询文本进行向量化
        
        Args:
            text: 查询文本
            
        Returns:
            向量
        """
        try:
            # 对单个查询进行编码
            embedding = self.model.encode([text], convert_to_tensor=False)
            
            # 确保返回的是List[float]格式
            if isinstance(embedding, np.ndarray):
                if embedding.ndim == 2:
                    embedding = embedding[0]  # 取第一个（也是唯一的）向量
                embedding = embedding.tolist()
            
            return embedding
            
        except Exception as e:
            raise Exception(f"查询向量化失败: {str(e)}")
    
    def get_model_info(self) -> dict:
        """
        获取模型信息
        
        Returns:
            模型信息字典
        """
        try:
            # 获取向量维度
            test_embedding = self.embed_query("test")
            dimension = len(test_embedding)
            
            return {
                "model_name": self.model_name,
                "dimension": dimension,
                "type": "local_sentence_transformer",
                "available": True
            }
        except Exception as e:
            return {
                "model_name": self.model_name,
                "dimension": 0,
                "type": "local_sentence_transformer",
                "available": False,
                "error": str(e)
            }


class FallbackEmbeddings(Embeddings):
    """备用Embedding类，当sentence-transformers不可用时使用简单的词向量"""
    
    def __init__(self):
        """初始化备用embedding"""
        print("⚠️  使用备用embedding方案（功能有限）")
        self.dimension = 384  # 固定维度
    
    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """简单的文档向量化（基于字符hash）"""
        embeddings = []
        for text in texts:
            # 简单的hash-based embedding
            embedding = self._text_to_vector(text)
            embeddings.append(embedding)
        return embeddings
    
    def embed_query(self, text: str) -> List[float]:
        """简单的查询向量化"""
        return self._text_to_vector(text)
    
    def _text_to_vector(self, text: str) -> List[float]:
        """将文本转换为向量（简单实现）"""
        # 这是一个非常简单的实现，仅用于演示
        # 实际使用中应该安装sentence-transformers
        import hashlib
        
        # 使用文本的hash值生成向量
        hash_obj = hashlib.md5(text.encode())
        hash_bytes = hash_obj.digest()
        
        # 将hash字节转换为浮点数向量
        vector = []
        for i in range(0, len(hash_bytes), 4):
            chunk = hash_bytes[i:i+4]
            if len(chunk) == 4:
                value = int.from_bytes(chunk, byteorder='big') / (2**32)
                vector.append(value)
        
        # 填充到固定维度
        while len(vector) < self.dimension:
            vector.extend(vector[:min(len(vector), self.dimension - len(vector))])
        
        return vector[:self.dimension]
    
    def get_model_info(self) -> dict:
        """获取备用模型信息"""
        return {
            "model_name": "fallback_hash_embedding",
            "dimension": self.dimension,
            "type": "fallback",
            "available": True,
            "warning": "这是备用方案，建议安装sentence-transformers获得更好效果"
        }


def get_local_embeddings(model_name: str = "all-MiniLM-L6-v2") -> Embeddings:
    """
    获取本地embedding实例
    
    Args:
        model_name: 模型名称
        
    Returns:
        Embedding实例
    """
    if SENTENCE_TRANSFORMERS_AVAILABLE:
        try:
            return LocalEmbeddings(model_name)
        except Exception as e:
            print(f"本地embedding初始化失败: {e}")
            print("使用备用方案...")
            return FallbackEmbeddings()
    else:
        print("sentence-transformers未安装，使用备用方案")
        return FallbackEmbeddings()


if __name__ == "__main__":
    # 测试本地embedding
    print("测试本地embedding...")
    
    embeddings = get_local_embeddings()
    info = embeddings.get_model_info()
    print(f"模型信息: {info}")
    
    # 测试向量化
    test_texts = ["这是一个测试文档", "这是另一个测试文档"]
    doc_embeddings = embeddings.embed_documents(test_texts)
    query_embedding = embeddings.embed_query("测试查询")
    
    print(f"文档向量数量: {len(doc_embeddings)}")
    print(f"向量维度: {len(doc_embeddings[0]) if doc_embeddings else 0}")
    print(f"查询向量维度: {len(query_embedding)}")
    print("✓ 本地embedding测试完成")
