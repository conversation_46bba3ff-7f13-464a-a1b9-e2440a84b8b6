"""
问答系统模块
基于向量检索和LLM的问答功能
"""

from typing import List, Dict, Any, Optional
from langchain.chains import RetrievalQA
from langchain.chat_models import ChatOpenAI
from langchain.prompts import PromptTemplate
from langchain.schema import Document
try:
    from .vector_store import VectorStore
except ImportError:
    from vector_store import VectorStore


class QASystem:
    """问答系统"""
    
    def __init__(self, 
                 model_name: str = "gpt-3.5-turbo",
                 temperature: float = 0.1,
                 max_tokens: Optional[int] = None):
        """
        初始化问答系统
        
        Args:
            model_name: LLM模型名称
            temperature: 生成温度
            max_tokens: 最大token数
        """
        self.model_name = model_name
        self.temperature = temperature
        self.max_tokens = max_tokens
        
        # 初始化LLM
        llm_kwargs = {
            "model_name": model_name,
            "temperature": temperature
        }
        if max_tokens:
            llm_kwargs["max_tokens"] = max_tokens
            
        self.llm = ChatOpenAI(**llm_kwargs)
        
        # 向量存储
        self.vector_store = None
        self.qa_chain = None
        
        # 自定义提示模板
        self.prompt_template = PromptTemplate(
            template="""基于以下上下文信息回答问题。如果上下文中没有相关信息，请说"根据提供的文档，我无法找到相关信息"。

上下文信息:
{context}

问题: {question}

请提供准确、简洁的回答:""",
            input_variables=["context", "question"]
        )
    
    def setup_qa_chain(self, vector_store: VectorStore, search_k: int = 4) -> None:
        """
        设置问答链
        
        Args:
            vector_store: 向量存储对象
            search_k: 检索的文档数量
        """
        if vector_store.vectorstore is None:
            raise ValueError("向量存储未初始化")
        
        self.vector_store = vector_store
        
        # 获取检索器
        retriever = vector_store.get_retriever(search_kwargs={"k": search_k})
        
        # 创建问答链
        self.qa_chain = RetrievalQA.from_chain_type(
            llm=self.llm,
            chain_type="stuff",
            retriever=retriever,
            return_source_documents=True,
            chain_type_kwargs={"prompt": self.prompt_template}
        )
        
        print(f"问答系统已设置，使用模型: {self.model_name}")
    
    def ask(self, question: str) -> Dict[str, Any]:
        """
        提问并获取答案
        
        Args:
            question: 用户问题
            
        Returns:
            包含答案和来源文档的字典
        """
        if self.qa_chain is None:
            raise ValueError("问答链未设置，请先调用setup_qa_chain方法")
        
        try:
            # 执行问答
            result = self.qa_chain({"query": question})
            
            # 整理返回结果
            response = {
                "question": question,
                "answer": result["result"],
                "source_documents": []
            }
            
            # 处理来源文档
            for doc in result["source_documents"]:
                source_info = {
                    "content": doc.page_content[:200] + "..." if len(doc.page_content) > 200 else doc.page_content,
                    "metadata": doc.metadata
                }
                response["source_documents"].append(source_info)
            
            return response
            
        except Exception as e:
            raise Exception(f"问答处理失败: {str(e)}")
    
    def batch_ask(self, questions: List[str]) -> List[Dict[str, Any]]:
        """
        批量提问
        
        Args:
            questions: 问题列表
            
        Returns:
            答案列表
        """
        results = []
        for question in questions:
            try:
                result = self.ask(question)
                results.append(result)
            except Exception as e:
                results.append({
                    "question": question,
                    "answer": f"处理失败: {str(e)}",
                    "source_documents": []
                })
        return results
    
    def search_documents(self, query: str, k: int = 4) -> List[Dict[str, Any]]:
        """
        仅进行文档检索，不生成答案
        
        Args:
            query: 查询文本
            k: 返回文档数量
            
        Returns:
            检索到的文档列表
        """
        if self.vector_store is None:
            raise ValueError("向量存储未设置")
        
        try:
            # 进行相似度搜索
            docs_with_scores = self.vector_store.similarity_search_with_score(query, k=k)
            
            results = []
            for doc, score in docs_with_scores:
                result = {
                    "content": doc.page_content,
                    "metadata": doc.metadata,
                    "similarity_score": float(score)
                }
                results.append(result)
            
            return results
            
        except Exception as e:
            raise Exception(f"文档检索失败: {str(e)}")
    
    def get_system_info(self) -> Dict[str, Any]:
        """
        获取系统信息
        
        Returns:
            系统信息字典
        """
        return {
            "model_name": self.model_name,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "qa_chain_ready": self.qa_chain is not None,
            "vector_store_ready": self.vector_store is not None
        }


if __name__ == "__main__":
    # 测试代码
    qa_system = QASystem()
    
    # 示例使用流程:
    # 1. 创建向量存储
    # vector_store = VectorStore()
    # vector_store.load_vectorstore("./vector_index")
    
    # 2. 设置问答系统
    # qa_system.setup_qa_chain(vector_store)
    
    # 3. 提问
    # result = qa_system.ask("你的问题")
    # print(result["answer"])
