# Mini RAG System

一个最小可行的RAG（检索增强生成）系统，支持PDF文档处理、向量检索和智能问答。

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆或下载项目
# cd mini_rag_project

# 安装依赖（使用最新版本）
pip install -r requirements.txt

# 配置API密钥
cp .env.example .env
# 编辑 .env 文件，设置你的 OPENAI_API_KEY
```

### 1.1 依赖说明

本项目使用最新版本的LangChain生态系统：
- `langchain` >= 0.1.0 - 核心框架
- `langchain-community` >= 0.0.10 - 社区组件
- `langchain-openai` >= 0.0.5 - OpenAI集成
- `faiss-cpu` >= 1.8.0 - 向量数据库
- `pymupdf` >= 1.24.0 - PDF处理

### 2. 运行系统

```bash
# 方式1: 交互式运行
python main.py

# 方式2: 查看使用示例
python example_usage.py

# 方式3: 运行简单测试
python simple_test.py
```

### 3. 使用流程

1. **构建知识库**：选择选项1，输入PDF文件路径
2. **开始问答**：系统自动进入交互模式
3. **提问**：输入问题获取答案
4. **搜索**：输入 `search:关键词` 搜索相关文档
5. **退出**：输入 `quit` 或 `exit` 退出系统

## 📁 项目结构

```
mini_rag_project/
├── main.py                 # 主程序入口
├── example_usage.py       # 使用示例脚本
├── simple_test.py         # 简单测试脚本
├── test_rag.py            # 完整测试脚本
├── requirements.txt       # 依赖包列表（最新版本）
├── .env.example          # 环境变量模板
├── README.md             # 使用文档
└── src/                  # 源代码目录
    ├── __init__.py
    ├── document_processor.py  # PDF处理模块
    ├── vector_store.py       # 向量存储模块
    └── qa_system.py         # 问答系统模块
```

## 🔧 核心功能

### 1. PDF文档处理
- 支持PDF文档加载和解析
- 智能文档切分（按段落、章节等）
- 保留文档元数据信息

### 2. 向量存储
- 使用OpenAI Embedding模型
- FAISS向量数据库存储
- 支持向量索引的保存和加载

### 3. 智能问答
- 基于向量相似度的文档检索
- LLM生成准确答案
- 返回答案来源文档

## 📖 API使用示例

### 基础使用

```python
from main import MiniRAG

# 创建RAG系统
rag = MiniRAG()

# 构建知识库
rag.build_knowledge_base("document.pdf")

# 提问
result = rag.ask("这个文档讲了什么？")
print(result["answer"])

# 搜索相关文档
docs = rag.search("关键词", k=3)
```

### 模块化使用

```python
from src.document_processor import DocumentProcessor
from src.vector_store import VectorStore
from src.qa_system import QASystem

# 1. 处理文档
processor = DocumentProcessor()
documents = processor.process_pdf("document.pdf")

# 2. 创建向量存储
vector_store = VectorStore()
vector_store.create_vectorstore(documents)
vector_store.save_vectorstore("./vector_index")

# 3. 设置问答系统
qa_system = QASystem()
qa_system.setup_qa_chain(vector_store)

# 4. 提问
result = qa_system.ask("你的问题")
```

## ⚙️ 配置选项

### 环境变量

```bash
# OpenAI API配置
OPENAI_API_KEY=your_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1  # 可选，默认OpenAI官方API
```

### 系统参数

```python
# 创建自定义配置的RAG系统
rag = MiniRAG(
    chunk_size=1000,           # 文档切分块大小
    chunk_overlap=100,         # 块重叠大小
    embedding_model="text-embedding-3-small",  # embedding模型
    llm_model="gpt-4"         # LLM模型
)
```

## 🧪 测试

```bash
# 运行测试套件
python test_rag.py
```

测试包括：
- 各模块功能测试
- 系统集成测试
- API接口验证

## 📋 系统要求

- Python 3.8+
- OpenAI API密钥
- 至少2GB可用内存（用于向量存储）

## 🔍 支持的功能

### 文档格式
- ✅ PDF文档
- ❌ Word文档（计划支持）
- ❌ 文本文件（计划支持）

### 检索方式
- ✅ 语义相似度检索
- ❌ 关键词检索（计划支持）
- ❌ 混合检索（计划支持）

### LLM模型
- ✅ OpenAI GPT系列
- ✅ 兼容OpenAI API的其他模型
- ❌ 本地模型（计划支持）

## 🚨 注意事项

1. **API费用**：使用OpenAI API会产生费用，请合理控制使用量
2. **文档大小**：建议单个PDF文件不超过50MB
3. **网络连接**：需要稳定的网络连接访问OpenAI API
4. **数据隐私**：文档内容会发送到OpenAI服务器，请注意数据安全

## 🛠️ 故障排除

### 常见问题

**Q: 提示"API密钥未设置"**
A: 检查.env文件是否正确配置OPENAI_API_KEY

**Q: PDF加载失败**
A: 确认PDF文件路径正确且文件未损坏

**Q: 向量存储加载失败**
A: 确认向量索引目录存在且完整

**Q: 问答响应慢**
A: 检查网络连接，考虑使用更快的embedding模型

### 错误代码

- `FileNotFoundError`: 文件路径不存在
- `ValueError`: 参数配置错误
- `Exception`: API调用失败或网络问题

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！
