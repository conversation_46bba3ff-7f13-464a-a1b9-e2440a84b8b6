# Mini RAG System 快速开始指南

## 🎯 解决向量存储本地化问题

针对您遇到的API调用失败问题，我们已经实现了**完全本地化的向量存储方案**，无需任何外部API调用。

## 🚀 立即开始（无需API密钥）

### 1. 安装依赖

```bash
# 安装基础依赖
pip install langchain langchain-community faiss-cpu pymupdf python-dotenv

# 安装本地embedding模型（重要！）
pip install sentence-transformers torch transformers
```

### 2. 运行本地模式

```bash
# 直接运行本地模式演示
python local_mode_example.py
```

### 3. 使用流程

1. **选择操作1** - 构建新的本地知识库
2. **输入PDF路径** - 提供您的PDF文件路径
3. **等待处理** - 系统会自动下载并使用本地模型
4. **开始搜索** - 输入关键词搜索相关文档

## 🔧 技术方案

### 本地Embedding模型

我们使用 `sentence-transformers` 库提供的预训练模型：

- **all-MiniLM-L6-v2** (默认): 轻量级，速度快，384维向量
- **all-mpnet-base-v2**: 质量更好，768维向量
- **paraphrase-multilingual-MiniLM-L12-v2**: 支持多语言

### 向量存储

- **FAISS**: 本地向量数据库，无需网络连接
- **本地保存**: 向量索引保存在本地文件系统
- **快速加载**: 支持增量更新和快速检索

## 📊 功能对比

| 功能 | 本地模式 | 原API模式 |
|------|----------|-----------|
| PDF处理 | ✅ 完全本地 | ✅ |
| 文档向量化 | ✅ 本地模型 | ❌ API失败 |
| 向量存储 | ✅ FAISS本地 | ✅ |
| 文档检索 | ✅ 本地计算 | ✅ |
| 智能问答 | ⚠️ 需要LLM API | ⚠️ 需要LLM API |

## 🎮 使用示例

### 基础搜索示例

```python
from local_mode_example import LocalRAG

# 1. 创建本地RAG系统
local_rag = LocalRAG()

# 2. 构建知识库
local_rag.build_knowledge_base("your_document.pdf")

# 3. 搜索文档
results = local_rag.search_documents("技术架构", k=3)

# 4. 查看结果
for i, (doc, score) in enumerate(results, 1):
    print(f"文档 {i} (相似度: {score:.3f})")
    print(f"内容: {doc.page_content[:200]}...")
```

### 交互式使用

```bash
# 运行交互式搜索
python local_mode_example.py

# 按提示操作：
# 1. 选择"构建新的本地知识库"
# 2. 输入PDF文件路径
# 3. 等待处理完成
# 4. 输入搜索查询
# 5. 查看搜索结果
```

## 🔍 搜索效果

本地embedding模型虽然不如OpenAI的模型，但对于文档检索任务已经足够好用：

- **语义理解**: 能理解同义词和相关概念
- **中文支持**: 支持中文文档处理
- **快速响应**: 本地计算，无网络延迟
- **隐私保护**: 文档不会发送到外部服务器

## 🛠️ 故障排除

### 常见问题

**Q: 首次运行很慢？**
A: 首次运行会下载embedding模型（约100MB），后续使用会很快

**Q: 内存不足？**
A: 可以使用更小的模型，如 `all-MiniLM-L6-v2`

**Q: 中文效果不好？**
A: 可以尝试多语言模型 `paraphrase-multilingual-MiniLM-L12-v2`

### 模型选择

```python
# 轻量级模型（推荐）
local_rag = LocalRAG("all-MiniLM-L6-v2")

# 高质量模型
local_rag = LocalRAG("all-mpnet-base-v2")

# 多语言模型
local_rag = LocalRAG("paraphrase-multilingual-MiniLM-L12-v2")
```

## 🎯 下一步

1. **测试本地模式**: 使用 `python local_mode_example.py`
2. **处理您的PDF**: 构建专属知识库
3. **优化搜索**: 尝试不同的查询方式
4. **扩展功能**: 如需问答功能，可配置LLM API

## 💡 优势总结

✅ **完全本地化** - 无需任何外部API
✅ **隐私保护** - 文档不离开本地环境  
✅ **成本控制** - 无API调用费用
✅ **稳定可靠** - 不受网络和API服务影响
✅ **快速响应** - 本地计算，响应迅速

现在您可以完全避开API调用问题，使用纯本地的RAG系统进行文档处理和检索！
