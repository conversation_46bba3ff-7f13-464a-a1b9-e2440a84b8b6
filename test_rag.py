"""
Mini RAG System 测试脚本
验证系统各模块功能的正确性
"""

import os
import sys
import tempfile
import shutil
from dotenv import load_dotenv

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.document_processor import DocumentProcessor
from src.vector_store import VectorStore
from src.qa_system import QASystem
from main import MiniRAG


def test_document_processor():
    """测试文档处理器"""
    print("=== 测试文档处理器 ===")
    
    processor = DocumentProcessor(chunk_size=500, chunk_overlap=50)
    
    # 测试基本功能
    print("✓ 文档处理器初始化成功")
    
    # 测试文档信息获取
    info = processor.get_document_info([])
    assert info["total_chunks"] == 0
    print("✓ 空文档信息获取正常")
    
    print("文档处理器测试完成\n")


def test_vector_store():
    """测试向量存储"""
    print("=== 测试向量存储 ===")
    
    # 检查API密钥
    if not os.getenv("OPENAI_API_KEY"):
        print("⚠️  跳过向量存储测试 (需要OPENAI_API_KEY)")
        return
    
    try:
        vector_store = VectorStore()
        print("✓ 向量存储初始化成功")
        
        # 测试基本信息
        info = vector_store.get_vectorstore_info()
        assert info["status"] == "未初始化"
        print("✓ 向量存储状态检查正常")
        
        print("向量存储测试完成\n")
        
    except Exception as e:
        print(f"⚠️  向量存储测试失败: {str(e)}\n")


def test_qa_system():
    """测试问答系统"""
    print("=== 测试问答系统 ===")
    
    # 检查API密钥
    if not os.getenv("OPENAI_API_KEY"):
        print("⚠️  跳过问答系统测试 (需要OPENAI_API_KEY)")
        return
    
    try:
        qa_system = QASystem()
        print("✓ 问答系统初始化成功")
        
        # 测试系统信息
        info = qa_system.get_system_info()
        assert info["model_name"] == "gpt-3.5-turbo"
        assert not info["qa_chain_ready"]
        print("✓ 问答系统状态检查正常")
        
        print("问答系统测试完成\n")
        
    except Exception as e:
        print(f"⚠️  问答系统测试失败: {str(e)}\n")


def test_mini_rag():
    """测试完整RAG系统"""
    print("=== 测试完整RAG系统 ===")
    
    try:
        rag = MiniRAG()
        print("✓ Mini RAG系统初始化成功")
        
        # 测试未就绪状态
        assert not rag.is_ready
        print("✓ 系统状态检查正常")
        
        print("Mini RAG系统测试完成\n")
        
    except Exception as e:
        print(f"⚠️  Mini RAG系统测试失败: {str(e)}\n")


def create_test_pdf():
    """创建测试PDF文件"""
    print("=== 创建测试PDF ===")
    
    try:
        # 这里我们创建一个简单的文本文件作为测试
        # 在实际使用中，用户需要提供真实的PDF文件
        test_content = """
        这是一个测试文档。
        
        第一章：介绍
        这是第一章的内容，介绍了系统的基本概念和原理。
        
        第二章：实现
        这是第二章的内容，详细描述了系统的实现方法。
        系统采用了先进的技术架构，确保了高性能和可扩展性。
        
        第三章：总结
        这是第三章的内容，总结了系统的优势和特点。
        系统具有良好的用户体验和强大的功能。
        """
        
        # 创建临时文本文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
            f.write(test_content)
            test_file_path = f.name
        
        print(f"✓ 创建测试文件: {test_file_path}")
        return test_file_path
        
    except Exception as e:
        print(f"⚠️  创建测试文件失败: {str(e)}")
        return None


def run_integration_test():
    """运行集成测试"""
    print("=== 集成测试 ===")
    
    # 检查API密钥
    if not os.getenv("OPENAI_API_KEY"):
        print("⚠️  跳过集成测试 (需要OPENAI_API_KEY)")
        print("请设置OPENAI_API_KEY环境变量后重新运行测试")
        return
    
    # 创建测试文件
    test_file = create_test_pdf()
    if not test_file:
        return
    
    # 创建临时目录用于向量存储
    temp_dir = tempfile.mkdtemp()
    vector_path = os.path.join(temp_dir, "test_vector_index")
    
    try:
        print("开始集成测试...")
        
        # 注意：这里使用文本文件而不是PDF进行测试
        # 在实际使用中需要PDF文件
        print("⚠️  注意：集成测试需要真实的PDF文件")
        print("⚠️  当前测试跳过PDF处理部分")
        
        print("✓ 集成测试框架验证完成")
        
    except Exception as e:
        print(f"⚠️  集成测试失败: {str(e)}")
    
    finally:
        # 清理临时文件
        try:
            os.unlink(test_file)
            shutil.rmtree(temp_dir)
            print("✓ 清理临时文件完成")
        except:
            pass


def main():
    """主测试函数"""
    print("Mini RAG System 测试套件")
    print("=" * 50)
    
    # 加载环境变量
    load_dotenv()
    
    # 运行各模块测试
    test_document_processor()
    test_vector_store()
    test_qa_system()
    test_mini_rag()
    
    # 运行集成测试
    run_integration_test()
    
    print("=" * 50)
    print("测试完成!")
    
    # 提供使用建议
    print("\n使用建议:")
    print("1. 确保已设置OPENAI_API_KEY环境变量")
    print("2. 准备PDF文件进行测试")
    print("3. 运行 python main.py 开始使用系统")
    
    if not os.getenv("OPENAI_API_KEY"):
        print("\n⚠️  警告: 未检测到OPENAI_API_KEY")
        print("请按照以下步骤设置:")
        print("1. 复制 .env.example 为 .env")
        print("2. 在 .env 文件中设置你的OpenAI API密钥")


if __name__ == "__main__":
    main()
