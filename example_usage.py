"""
Mini RAG System 使用示例
展示如何使用各个模块构建和使用RAG系统
"""

import os
import sys
from dotenv import load_dotenv

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from main import MiniRAG


def example_basic_usage():
    """基础使用示例"""
    print("=== 基础使用示例 ===")
    
    # 加载环境变量
    load_dotenv()
    
    # 检查API密钥
    if not os.getenv("OPENAI_API_KEY"):
        print("请先设置OPENAI_API_KEY环境变量")
        return
    
    # 创建RAG系统
    rag = MiniRAG(
        chunk_size=1000,           # 较小的块大小，适合测试
        chunk_overlap=100,         # 块重叠
        embedding_model="text-embedding-3-small",  # 最新的embedding模型
        llm_model="gpt-3.5-turbo"  # 使用GPT-3.5
    )
    
    # 示例PDF路径（需要用户提供真实的PDF文件）
    pdf_path = "example.pdf"
    
    if os.path.exists(pdf_path):
        try:
            # 构建知识库
            print(f"正在处理PDF文件: {pdf_path}")
            rag.build_knowledge_base(pdf_path, "./example_vector_index")
            
            # 示例问题
            questions = [
                "这个文档的主要内容是什么？",
                "文档中提到了哪些重要概念？",
                "有什么具体的实施建议吗？"
            ]
            
            # 批量提问
            for question in questions:
                print(f"\n问题: {question}")
                result = rag.ask(question)
                print(f"答案: {result['answer']}")
                print(f"参考文档数量: {len(result['source_documents'])}")
                
        except Exception as e:
            print(f"处理失败: {str(e)}")
    else:
        print(f"PDF文件不存在: {pdf_path}")
        print("请将您的PDF文件重命名为 'example.pdf' 并放在当前目录")


def example_modular_usage():
    """模块化使用示例"""
    print("\n=== 模块化使用示例 ===")
    
    # 加载环境变量
    load_dotenv()
    
    if not os.getenv("OPENAI_API_KEY"):
        print("请先设置OPENAI_API_KEY环境变量")
        return
    
    try:
        from src.document_processor import DocumentProcessor
        from src.vector_store import VectorStore
        from src.qa_system import QASystem
        
        # 1. 文档处理
        print("1. 初始化文档处理器...")
        processor = DocumentProcessor(chunk_size=800, chunk_overlap=80)
        
        # 2. 向量存储
        print("2. 初始化向量存储...")
        vector_store = VectorStore(embedding_model="text-embedding-3-small")
        
        # 3. 问答系统
        print("3. 初始化问答系统...")
        qa_system = QASystem(
            model_name="gpt-3.5-turbo",
            temperature=0.1,
            max_tokens=1000
        )
        
        print("✓ 所有模块初始化成功")
        
        # 如果有现有的向量索引，可以直接加载
        vector_index_path = "./example_vector_index"
        if os.path.exists(vector_index_path):
            print(f"4. 加载现有向量索引: {vector_index_path}")
            vector_store.load_vectorstore(vector_index_path)
            qa_system.setup_qa_chain(vector_store)
            
            # 测试搜索功能
            print("5. 测试文档搜索...")
            search_results = qa_system.search_documents("重要概念", k=2)
            print(f"   找到 {len(search_results)} 个相关文档")
            
            for i, doc in enumerate(search_results, 1):
                print(f"   文档 {i}: 相似度 {doc['similarity_score']:.3f}")
                print(f"   内容预览: {doc['content'][:100]}...")
        else:
            print("4. 未找到现有向量索引，请先运行基础示例构建知识库")
            
    except ImportError as e:
        print(f"模块导入失败: {e}")
        print("请确保已安装所有依赖: pip install -r requirements.txt")
    except Exception as e:
        print(f"模块化使用失败: {str(e)}")


def example_advanced_features():
    """高级功能示例"""
    print("\n=== 高级功能示例 ===")
    
    # 加载环境变量
    load_dotenv()
    
    if not os.getenv("OPENAI_API_KEY"):
        print("请先设置OPENAI_API_KEY环境变量")
        return
    
    try:
        # 使用更高级的配置
        rag = MiniRAG(
            chunk_size=1500,           # 更大的块大小
            chunk_overlap=200,         # 更多重叠
            embedding_model="text-embedding-3-large",  # 更好的embedding模型
            llm_model="gpt-4"          # 使用GPT-4
        )
        
        vector_index_path = "./example_vector_index"
        if os.path.exists(vector_index_path):
            print("加载知识库...")
            rag.load_knowledge_base(vector_index_path)
            
            # 高级搜索示例
            print("\n高级搜索功能:")
            search_queries = [
                "技术实现",
                "系统架构",
                "性能优化"
            ]
            
            for query in search_queries:
                print(f"\n搜索: {query}")
                docs = rag.search(query, k=3)
                for i, doc in enumerate(docs, 1):
                    print(f"  {i}. 相似度: {doc['similarity_score']:.3f}")
                    print(f"     内容: {doc['content'][:80]}...")
            
            # 复杂问答示例
            print("\n复杂问答:")
            complex_questions = [
                "请详细解释文档中的核心技术原理",
                "这个系统有什么优势和局限性？",
                "如何在实际项目中应用这些方法？"
            ]
            
            for question in complex_questions:
                print(f"\n问题: {question}")
                result = rag.ask(question)
                print(f"答案: {result['answer'][:200]}...")
                
        else:
            print("未找到向量索引，请先运行基础示例")
            
    except Exception as e:
        print(f"高级功能示例失败: {str(e)}")


def main():
    """主函数"""
    print("Mini RAG System 使用示例")
    print("=" * 50)
    
    print("本示例展示了如何使用Mini RAG系统的各种功能")
    print("请确保:")
    print("1. 已安装所有依赖: pip install -r requirements.txt")
    print("2. 已设置OPENAI_API_KEY环境变量")
    print("3. 准备了PDF文件用于测试")
    print()
    
    # 运行示例
    example_basic_usage()
    example_modular_usage()
    example_advanced_features()
    
    print("\n" + "=" * 50)
    print("示例完成!")
    print("\n要开始使用系统，请运行: python main.py")


if __name__ == "__main__":
    main()
