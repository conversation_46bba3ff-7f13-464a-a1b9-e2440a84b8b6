"""
简化测试脚本 - 验证Mini RAG系统基本功能
"""

import os
import sys

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """测试模块导入"""
    print("=== 测试模块导入 ===")
    
    try:
        from src.document_processor import DocumentProcessor
        print("✓ document_processor 导入成功")
    except Exception as e:
        print(f"✗ document_processor 导入失败: {e}")
        return False
    
    try:
        from src.vector_store import VectorStore
        print("✓ vector_store 导入成功")
    except Exception as e:
        print(f"✗ vector_store 导入失败: {e}")
        return False
    
    try:
        from src.qa_system import QASystem
        print("✓ qa_system 导入成功")
    except Exception as e:
        print(f"✗ qa_system 导入失败: {e}")
        return False
    
    try:
        from main import MiniRAG
        print("✓ main 导入成功")
    except Exception as e:
        print(f"✗ main 导入失败: {e}")
        return False
    
    return True

def test_basic_functionality():
    """测试基本功能"""
    print("\n=== 测试基本功能 ===")
    
    try:
        from src.document_processor import DocumentProcessor
        
        # 测试文档处理器初始化
        processor = DocumentProcessor()
        print("✓ DocumentProcessor 初始化成功")
        
        # 测试空文档信息
        info = processor.get_document_info([])
        assert info["total_chunks"] == 0
        print("✓ 空文档信息获取正常")
        
        return True
        
    except Exception as e:
        print(f"✗ 基本功能测试失败: {e}")
        return False

def test_environment():
    """测试环境配置"""
    print("\n=== 测试环境配置 ===")
    
    # 检查.env文件
    if os.path.exists('.env'):
        print("✓ .env 文件存在")
    else:
        print("⚠️  .env 文件不存在，请从 .env.example 复制")
    
    # 检查API密钥
    from dotenv import load_dotenv
    load_dotenv()
    
    if os.getenv("OPENAI_API_KEY"):
        print("✓ OPENAI_API_KEY 已设置")
        return True
    else:
        print("⚠️  OPENAI_API_KEY 未设置")
        return False

def main():
    """主测试函数"""
    print("Mini RAG System 简化测试")
    print("=" * 40)
    
    # 测试导入
    import_success = test_imports()
    
    # 测试基本功能
    basic_success = test_basic_functionality()
    
    # 测试环境
    env_success = test_environment()
    
    print("\n" + "=" * 40)
    print("测试结果:")
    print(f"模块导入: {'✓ 通过' if import_success else '✗ 失败'}")
    print(f"基本功能: {'✓ 通过' if basic_success else '✗ 失败'}")
    print(f"环境配置: {'✓ 通过' if env_success else '⚠️  需要配置'}")
    
    if import_success and basic_success:
        print("\n🎉 系统基本功能正常！")
        print("\n下一步:")
        print("1. 设置 OPENAI_API_KEY (如果还没有)")
        print("2. 准备PDF文件")
        print("3. 运行 python main.py 开始使用")
    else:
        print("\n❌ 系统存在问题，请检查依赖安装")
        print("运行: pip install -r requirements.txt")

if __name__ == "__main__":
    main()
