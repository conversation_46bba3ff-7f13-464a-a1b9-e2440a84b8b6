"""
Mini RAG System - 最小可行的RAG系统
整合PDF处理、向量存储和问答功能
"""

import os
import sys
from dotenv import load_dotenv
from typing import Optional

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.document_processor import DocumentProcessor
from src.vector_store import VectorStore
from src.qa_system import QASystem


class MiniRAG:
    """最小RAG系统主类"""
    
    def __init__(self, 
                 chunk_size: int = 1500,
                 chunk_overlap: int = 200,
                 embedding_model: str = "text-embedding-3-small",
                 llm_model: str = "gpt-3.5-turbo"):
        """
        初始化Mini RAG系统
        
        Args:
            chunk_size: 文档切分块大小
            chunk_overlap: 块重叠大小
            embedding_model: embedding模型
            llm_model: LLM模型
        """
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.embedding_model = embedding_model
        self.llm_model = llm_model
        
        # 初始化各模块
        self.doc_processor = DocumentProcessor(chunk_size, chunk_overlap)
        self.vector_store = VectorStore(embedding_model)
        self.qa_system = QASystem(llm_model)
        
        # 状态标记
        self.is_ready = False
        
    def build_knowledge_base(self, pdf_path: str, save_path: str = "./vector_index") -> None:
        """
        构建知识库：处理PDF -> 向量化 -> 保存
        
        Args:
            pdf_path: PDF文件路径
            save_path: 向量存储保存路径
        """
        print("=== 开始构建知识库 ===")
        
        # 1. 处理PDF文档
        print(f"1. 处理PDF文档: {pdf_path}")
        documents = self.doc_processor.process_pdf(pdf_path)
        
        # 显示文档信息
        doc_info = self.doc_processor.get_document_info(documents)
        print(f"   文档信息: {doc_info}")
        
        # 2. 创建向量存储
        print("2. 创建向量存储...")
        self.vector_store.create_vectorstore(documents)
        
        # 3. 保存向量存储
        print(f"3. 保存向量存储到: {save_path}")
        self.vector_store.save_vectorstore(save_path)
        
        # 4. 设置问答系统
        print("4. 设置问答系统...")
        self.qa_system.setup_qa_chain(self.vector_store)
        
        self.is_ready = True
        print("=== 知识库构建完成 ===\n")
    
    def load_knowledge_base(self, load_path: str = "./vector_index") -> None:
        """
        加载已有的知识库
        
        Args:
            load_path: 向量存储加载路径
        """
        print("=== 加载知识库 ===")
        
        # 1. 加载向量存储
        print(f"1. 从 {load_path} 加载向量存储...")
        self.vector_store.load_vectorstore(load_path)
        
        # 2. 设置问答系统
        print("2. 设置问答系统...")
        self.qa_system.setup_qa_chain(self.vector_store)
        
        self.is_ready = True
        print("=== 知识库加载完成 ===\n")
    
    def ask(self, question: str) -> dict:
        """
        提问
        
        Args:
            question: 用户问题
            
        Returns:
            答案字典
        """
        if not self.is_ready:
            raise ValueError("系统未就绪，请先构建或加载知识库")
        
        return self.qa_system.ask(question)
    
    def search(self, query: str, k: int = 4) -> list:
        """
        搜索相关文档
        
        Args:
            query: 查询文本
            k: 返回文档数量
            
        Returns:
            相关文档列表
        """
        if not self.is_ready:
            raise ValueError("系统未就绪，请先构建或加载知识库")
        
        return self.qa_system.search_documents(query, k)
    
    def interactive_mode(self):
        """交互式问答模式"""
        if not self.is_ready:
            print("错误: 系统未就绪，请先构建或加载知识库")
            return
        
        print("=== 进入交互式问答模式 ===")
        print("输入 'quit' 或 'exit' 退出")
        print("输入 'search:查询内容' 进行文档搜索")
        print("-" * 50)
        
        while True:
            try:
                user_input = input("\n请输入问题: ").strip()
                
                if user_input.lower() in ['quit', 'exit', '退出']:
                    print("再见!")
                    break
                
                if not user_input:
                    continue
                
                # 检查是否是搜索命令
                if user_input.startswith('search:'):
                    query = user_input[7:].strip()
                    if query:
                        print(f"\n搜索: {query}")
                        results = self.search(query, k=3)
                        for i, doc in enumerate(results, 1):
                            print(f"\n--- 文档 {i} (相似度: {doc['similarity_score']:.3f}) ---")
                            print(f"内容: {doc['content'][:300]}...")
                            print(f"来源: {doc['metadata']}")
                    continue
                
                # 普通问答
                print(f"\n问题: {user_input}")
                result = self.ask(user_input)
                
                print(f"\n答案: {result['answer']}")
                
                if result['source_documents']:
                    print(f"\n参考文档 ({len(result['source_documents'])} 个):")
                    for i, doc in enumerate(result['source_documents'], 1):
                        print(f"  {i}. {doc['content']}")
                        print(f"     来源: {doc['metadata']}")
                
            except KeyboardInterrupt:
                print("\n\n再见!")
                break
            except Exception as e:
                print(f"错误: {str(e)}")


def main():
    """主函数"""
    # 加载环境变量
    load_dotenv()
    
    # 检查API密钥
    if not os.getenv("OPENAI_API_KEY"):
        print("错误: 请设置OPENAI_API_KEY环境变量")
        print("1. 复制 .env.example 为 .env")
        print("2. 在 .env 文件中设置你的API密钥")
        return
    
    # 创建RAG系统
    rag = MiniRAG()
    
    print("=== Mini RAG System ===")
    print("1. 构建新知识库")
    print("2. 加载已有知识库")
    print("3. 退出")
    
    while True:
        choice = input("\n请选择操作 (1-3): ").strip()
        
        if choice == "1":
            pdf_path = input("请输入PDF文件路径: ").strip()
            if os.path.exists(pdf_path):
                try:
                    rag.build_knowledge_base(pdf_path)
                    rag.interactive_mode()
                    break
                except Exception as e:
                    print(f"构建知识库失败: {str(e)}")
            else:
                print("文件不存在，请检查路径")
        
        elif choice == "2":
            index_path = input("请输入向量索引路径 (默认: ./vector_index): ").strip()
            if not index_path:
                index_path = "./vector_index"
            
            if os.path.exists(index_path):
                try:
                    rag.load_knowledge_base(index_path)
                    rag.interactive_mode()
                    break
                except Exception as e:
                    print(f"加载知识库失败: {str(e)}")
            else:
                print("向量索引不存在，请先构建知识库")
        
        elif choice == "3":
            print("再见!")
            break
        
        else:
            print("无效选择，请重新输入")


if __name__ == "__main__":
    main()
