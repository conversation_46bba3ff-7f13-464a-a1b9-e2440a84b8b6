"""
本地模式示例 - 无需API密钥的RAG系统演示
使用本地embedding模型和文档检索功能
"""

import os
import sys

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.document_processor import DocumentProcessor
from src.vector_store import VectorStore
from src.local_embeddings import get_local_embeddings


class LocalRAG:
    """本地RAG系统（仅文档处理和检索，无LLM问答）"""
    
    def __init__(self, embedding_model: str = "all-MiniLM-L6-v2"):
        """
        初始化本地RAG系统
        
        Args:
            embedding_model: 本地embedding模型名称
        """
        print("=== 初始化本地RAG系统 ===")
        
        # 初始化文档处理器
        self.doc_processor = DocumentProcessor(chunk_size=1000, chunk_overlap=100)
        print("✓ 文档处理器初始化完成")
        
        # 初始化向量存储（强制使用本地模式）
        self.vector_store = VectorStore(embedding_model=embedding_model, use_local=True)
        print("✓ 向量存储初始化完成")
        
        self.is_ready = False
    
    def build_knowledge_base(self, pdf_path: str, save_path: str = "./local_vector_index"):
        """
        构建本地知识库
        
        Args:
            pdf_path: PDF文件路径
            save_path: 向量存储保存路径
        """
        print(f"\n=== 构建本地知识库 ===")
        print(f"PDF文件: {pdf_path}")
        
        # 1. 处理PDF文档
        print("1. 处理PDF文档...")
        documents = self.doc_processor.process_pdf(pdf_path)
        
        # 显示文档信息
        doc_info = self.doc_processor.get_document_info(documents)
        print(f"   文档统计: {doc_info}")
        
        # 2. 创建向量存储
        print("2. 创建本地向量存储...")
        self.vector_store.create_vectorstore(documents)
        
        # 3. 保存向量存储
        print(f"3. 保存向量存储到: {save_path}")
        self.vector_store.save_vectorstore(save_path)
        
        self.is_ready = True
        print("✓ 本地知识库构建完成")
    
    def load_knowledge_base(self, load_path: str = "./local_vector_index"):
        """
        加载本地知识库
        
        Args:
            load_path: 向量存储加载路径
        """
        print(f"\n=== 加载本地知识库 ===")
        print(f"加载路径: {load_path}")
        
        self.vector_store.load_vectorstore(load_path)
        self.is_ready = True
        print("✓ 本地知识库加载完成")
    
    def search_documents(self, query: str, k: int = 5):
        """
        搜索相关文档
        
        Args:
            query: 查询文本
            k: 返回文档数量
            
        Returns:
            相关文档列表
        """
        if not self.is_ready:
            raise ValueError("知识库未就绪，请先构建或加载知识库")
        
        print(f"\n搜索查询: {query}")
        results = self.vector_store.similarity_search_with_score(query, k=k)
        
        print(f"找到 {len(results)} 个相关文档:")
        for i, (doc, score) in enumerate(results, 1):
            print(f"\n--- 文档 {i} (相似度: {score:.3f}) ---")
            print(f"内容: {doc.page_content[:200]}...")
            if doc.metadata:
                print(f"元数据: {doc.metadata}")
        
        return results
    
    def interactive_search(self):
        """交互式搜索模式"""
        if not self.is_ready:
            print("错误: 知识库未就绪，请先构建或加载知识库")
            return
        
        print("\n=== 进入交互式搜索模式 ===")
        print("输入 'quit' 或 'exit' 退出")
        print("输入查询内容进行文档搜索")
        print("-" * 50)
        
        while True:
            try:
                query = input("\n请输入搜索查询: ").strip()
                
                if query.lower() in ['quit', 'exit', '退出']:
                    print("再见!")
                    break
                
                if not query:
                    continue
                
                # 执行搜索
                self.search_documents(query, k=3)
                
            except KeyboardInterrupt:
                print("\n\n再见!")
                break
            except Exception as e:
                print(f"搜索错误: {str(e)}")


def test_local_embeddings():
    """测试本地embedding功能"""
    print("=== 测试本地Embedding ===")
    
    try:
        # 获取本地embedding实例
        embeddings = get_local_embeddings("all-MiniLM-L6-v2")
        
        # 获取模型信息
        info = embeddings.get_model_info()
        print(f"模型信息: {info}")
        
        # 测试向量化
        test_texts = ["这是一个测试文档", "这是另一个测试文档"]
        print(f"测试文本: {test_texts}")
        
        doc_embeddings = embeddings.embed_documents(test_texts)
        query_embedding = embeddings.embed_query("测试查询")
        
        print(f"✓ 文档向量数量: {len(doc_embeddings)}")
        print(f"✓ 向量维度: {len(doc_embeddings[0]) if doc_embeddings else 0}")
        print(f"✓ 查询向量维度: {len(query_embedding)}")
        
        return True
        
    except Exception as e:
        print(f"✗ 本地embedding测试失败: {e}")
        return False


def main():
    """主函数"""
    print("本地RAG系统演示")
    print("=" * 50)
    print("本系统使用本地embedding模型，无需API密钥")
    print("功能包括:")
    print("✓ PDF文档处理和切分")
    print("✓ 本地向量化（sentence-transformers）")
    print("✓ 文档相似度搜索")
    print("✗ LLM问答（需要API密钥）")
    print()
    
    # 测试本地embedding
    if not test_local_embeddings():
        print("\n请安装sentence-transformers:")
        print("pip install sentence-transformers")
        return
    
    # 创建本地RAG系统
    local_rag = LocalRAG()
    
    print("\n选择操作:")
    print("1. 构建新的本地知识库")
    print("2. 加载已有的本地知识库")
    print("3. 退出")
    
    while True:
        choice = input("\n请选择操作 (1-3): ").strip()
        
        if choice == "1":
            pdf_path = input("请输入PDF文件路径: ").strip()
            if os.path.exists(pdf_path):
                try:
                    local_rag.build_knowledge_base(pdf_path)
                    local_rag.interactive_search()
                    break
                except Exception as e:
                    print(f"构建知识库失败: {str(e)}")
            else:
                print("文件不存在，请检查路径")
        
        elif choice == "2":
            index_path = input("请输入向量索引路径 (默认: ./local_vector_index): ").strip()
            if not index_path:
                index_path = "./local_vector_index"
            
            if os.path.exists(index_path):
                try:
                    local_rag.load_knowledge_base(index_path)
                    local_rag.interactive_search()
                    break
                except Exception as e:
                    print(f"加载知识库失败: {str(e)}")
            else:
                print("向量索引不存在，请先构建知识库")
        
        elif choice == "3":
            print("再见!")
            break
        
        else:
            print("无效选择，请重新输入")


if __name__ == "__main__":
    main()
